-- Table: public.wallet_interest_settings

-- DROP TABLE IF EXISTS public.wallet_interest_settings;

CREATE TABLE IF NOT EXISTS public.wallet_interest_settings
(
    id integer NOT NULL DEFAULT nextval('wallet_interest_settings_id_seq'::regclass),
    key text COLLATE pg_catalog."default" NOT NULL,
    value text COLLATE pg_catalog."default" NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by integer NOT NULL,
    CONSTRAINT wallet_interest_settings_pkey PRIMARY KEY (id),
    CONSTRAINT wallet_interest_settings_setting_key_key UNIQUE (key),
    CONSTRAINT wallet_interest_settings_updated_by_fkey FOREIGN KEY (updated_by)
        REFERENCES public.users (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.wallet_interest_settings
    OWNER to "TevcDev";
-- Index: idx_wallet_interest_settings_key

-- DROP INDEX IF EXISTS public.idx_wallet_interest_settings_key;

CREATE UNIQUE INDEX IF NOT EXISTS idx_wallet_interest_settings_key
    ON public.wallet_interest_settings USING btree
    (key COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;