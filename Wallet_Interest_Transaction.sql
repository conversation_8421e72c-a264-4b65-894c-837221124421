-- Table: public.wallet_interest_transaction

-- DROP TABLE IF EXISTS public.wallet_interest_transaction;

CREATE TABLE IF NOT EXISTS public.wallet_interest_transaction
(
    id bigint NOT NULL DEFAULT nextval('wallet_interest_transaction_id_seq'::regclass),
    wallet_id bigint NOT NULL,
    transaction_date date NOT NULL,
    balance numeric NOT NULL,
    process_status character varying(20) COLLATE pg_catalog."default" NOT NULL DEFAULT 'Pending'::character varying,
    processed_at timestamp without time zone,
    currency character(5) COLLATE pg_catalog."default" DEFAULT 'NGN'::bpchar,
    interest_earned numeric(10,2) DEFAULT 0.00,
    fees numeric(10,2) DEFAULT 0.00,
    process_type character varying(50) COLLATE pg_catalog."default",
    CONSTRAINT wallet_interest_transaction_pkey PRIMARY KEY (id),
    CONSTRAINT "Wallet_Date_Unique" UNIQUE NULLS NOT DISTINCT (wallet_id, transaction_date, process_type),
    CONSTRAINT wallet_interest_transaction_wallet_id_fkey FOREIGN KEY (wallet_id)
        REFERENCES public.wallet (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.wallet_interest_transaction
    OWNER to "TevcDev";
-- Index: idx_wallet_interest_transaction

-- DROP INDEX IF EXISTS public.idx_wallet_interest_transaction;

CREATE INDEX IF NOT EXISTS idx_wallet_interest_transaction
    ON public.wallet_interest_transaction USING btree
    (wallet_id ASC NULLS LAST, transaction_date ASC NULLS LAST)
    TABLESPACE pg_default;
-- Index: idx_wallet_interest_transaction_process

-- DROP INDEX IF EXISTS public.idx_wallet_interest_transaction_process;

CREATE INDEX IF NOT EXISTS idx_wallet_interest_transaction_process
    ON public.wallet_interest_transaction USING btree
    (process_status COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;
-- Index: idx_wallet_interest_transaction_wallet_id_date

-- DROP INDEX IF EXISTS public.idx_wallet_interest_transaction_wallet_id_date;

CREATE INDEX IF NOT EXISTS idx_wallet_interest_transaction_wallet_id_date
    ON public.wallet_interest_transaction USING btree
    (wallet_id ASC NULLS LAST, transaction_date DESC NULLS FIRST)
    TABLESPACE pg_default;