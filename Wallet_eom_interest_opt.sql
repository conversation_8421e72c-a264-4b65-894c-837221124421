-- PROCEDURE: public.wallet_eom_interest_opt(date)

-- DROP PROCEDURE IF EXISTS public.wallet_eom_interest_opt(date);

CREATE OR REPLACE PROCEDURE public.wallet_eom_interest_opt(
	IN p_processing_date date)
LANGUAGE 'plpgsql'
AS $BODY$
DECLARE
    v_okay INT := 0;
    v_failed INT := 0;
    v_failed_wallets TEXT;
    v_batch_msg VARCHAR;
    v_fee_rate NUMERIC;
    v_withdrawal_limit NUMERIC;
BEGIN
		--- To protect the from running any other than the first day of the month.
		IF EXTRACT(DAY FROM p_processing_date) != 1 THEN
		    RAISE NOTICE 'Procedure skipped: The processing date provided (%) is not the first day of a month. No action was taken.', p_processing_date;
		    RETURN; -- Exit the procedure gracefully
		END IF;

    -- It's more efficient to fetch these settings once.
    SELECT value INTO v_fee_rate FROM public.wallet_interest_settings WHERE UPPER(key) = UPPER('withholding_tax');
    SELECT value INTO v_withdrawal_limit FROM public.wallet_interest_settings WHERE UPPER(key) = UPPER('withdrawal_limit');

    WITH valid_wallets AS (
        -- Select all wallets that are eligible for interest payment.
        SELECT
            id,
            available_balance,
            currency,
            current_month_earn_interest,
            withdrawal_count
        FROM
            public.wallet
        WHERE
            status = TRUE AND current_month_earn_interest > 0.00
    ), interest_calculation AS (
        -- Calculate interest and fees for all eligible wallets.
        SELECT
            id,
            available_balance,
            currency,
            current_month_earn_interest,
            CASE
                WHEN withdrawal_count < v_withdrawal_limit THEN
                    ROUND(current_month_earn_interest * v_fee_rate, 2)
                ELSE 0.00
            END AS fee,
            CASE
                WHEN withdrawal_count < v_withdrawal_limit THEN
                    current_month_earn_interest - ROUND(current_month_earn_interest * v_fee_rate, 2)
                ELSE 0.00
            END AS interest
        FROM
            valid_wallets
    ), new_transactions AS (
        -- Insert new transactions and return the generated IDs.
        INSERT INTO transactions (
            application, application_module, currency, gateway_response_reference, last_modify_date,
            transaction_amount, transaction_event, transaction_value_amount, transaction_fee_amount, transaction_mode,
            transaction_request_reference, transaction_end_date, transaction_start_date, transaction_status, transaction_status_reason, transaction_type,
            transaction_description, transaction_createdby_fk, transaction_userprofile_fk
        )
        SELECT
            'DigiKolo', 'USER_MODULE', i.currency, 'Wallet_Month_Interest_' || i.id || '_' || TO_CHAR(NOW(), 'YYMMDDHHMISS'), NOW(),
            i.interest, 'Wallet_Interest', i.interest, i.fee, 'MonthlyInterest',
            i.id::TEXT, NOW(), p_processing_date, 'Approved', 'Completed', 'Credit',
            'Wallet interest Earned', 1, 1
        FROM
            interest_calculation i
        WHERE i.interest > 0.00 -- Only insert for those who actually get interest.
        RETURNING id, transaction_request_reference::INT as w_id
    ), transaction_fees AS (
        -- Log transaction fees for the new transactions.
        INSERT INTO transaction_fee (fee_calculated_value, fee_name, fee_operational_value, fee_type, transaction_fk)
        SELECT
            i.fee, 'Withholding Tax', v_fee_rate * 100, 'Percentage', nt.id
        FROM
            interest_calculation i
        JOIN
            new_transactions nt ON i.id = nt.w_id
        WHERE i.fee > 0.00
    ), updated_wallets AS (
        -- Update the wallet balances.
        UPDATE
            public.wallet w
        SET
            available_balance = w.available_balance + i.interest,
            booked_balance = w.booked_balance + i.interest,
            last_modify_date = NOW(),
            withdrawal_count = 0,
            earned_interest_todate = w.earned_interest_todate + i.current_month_earn_interest,
            current_month_earn_interest = 0.00
        FROM
            interest_calculation i
        WHERE
            w.id = i.id
        RETURNING w.id
    ), history AS (
        -- Insert into wallet history.
        INSERT INTO wallet_history (
            balance_after_amount, balance_before_event, event_amount, event_currency,
            event_start_date, event_end_date, transaction_event, transaction_type,
            transaction_fk, wallet_fk
        )
        SELECT
            i.available_balance + i.interest, i.available_balance, i.interest, i.currency,
            NOW(), NOW(), 'Monthly_Interest', 'Credit',
            nt.id, i.id
        FROM
            interest_calculation i
        JOIN
            new_transactions nt ON i.id = nt.w_id
    ), wallet_transaction AS (
			-- Insert all successful interest transactions in a single operation
	    INSERT INTO wallet_interest_transaction
	        (wallet_id, transaction_date, balance, process_status, processed_at, currency, interest_earned, fees, process_type)
	    SELECT
	        i.id,
	        p_processing_date,
	        i.available_balance,
	        CASE
            WHEN ic.interest > 0.00 THEN
                    'Successful'
                ELSE 'LimitIssue'
            END,
	        NOW(),
	        i.currency,
	        ic.interest,
	        ic.fee,
	        'Monthly'
	    FROM
	        valid_wallets i
	    JOIN
	        interest_calculation ic ON i.id = ic.id
	)
    -- Log the results of the batch operation.
    SELECT
        COUNT(uw.id),
        (SELECT COUNT(*) FROM valid_wallets) - COUNT(uw.id),
        STRING_AGG(vw.id::TEXT, ',')
    INTO
        v_okay, v_failed, v_failed_wallets
    FROM
        valid_wallets vw
    LEFT JOIN
        updated_wallets uw ON vw.id = uw.id;

    v_batch_msg := 'Processed for ' || p_processing_date || ', Success count:- ' || v_okay || ', Failed count:- ' || v_failed;
    IF v_failed > 0 THEN
        v_batch_msg := v_batch_msg || ' Details:- ' || v_failed_wallets;
    END IF;

    INSERT INTO public.wallet_batch_log(event, message, logtime)
    VALUES ('Wallet_Monthly_Interest', v_batch_msg, CURRENT_TIMESTAMP);

EXCEPTION
    WHEN OTHERS THEN
        -- Basic error logging. For a production system, you might want more detailed error info.
        v_batch_msg := 'An error occurred during Wallet_EOM_Interest processing for ' || p_processing_date || ': ' || SQLERRM;
        INSERT INTO public.wallet_batch_log(event, message, logtime)
        VALUES ('Wallet_Monthly_Interest_Error', v_batch_msg, CURRENT_TIMESTAMP);
END;
$BODY$;
ALTER PROCEDURE public.wallet_eom_interest_opt(date)
    OWNER TO "TevcDev";  ---- change to production users
