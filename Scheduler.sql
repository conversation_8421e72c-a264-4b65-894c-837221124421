--- enable cron --
CREATE EXTENSION IF NOT EXISTS pg_cron;


--- Daily confirm_net_inflow
SELECT cron.schedule(
    'confirm_net_inflow_job',
    '30 23 * * *', -- every day at 23:30
    $$CALL confirm_net_inflow_1(current_date);$$
);

select * from cron.job

CALL confirm_net_inflow_1('2025-04-30');

select * from wallet_batch_log;

select * from wallet_interest_transaction
where wallet_id in (299, 15249,21086);

select count(1) from public.wallet_history 
where event_end_date::DATE = '2025-04-21'


---- Daily_Daily_Interest 
SELECT cron.schedule(
    'compute_daily_interest_job',
    '0 7 * * *', -- every day at 01:00
    $$CALL compute_daily_interest(current_date);$$
);

CALL compute_daily_interest(current_date);
select * from wallet_batch_log;

select * from wallet_interest_transaction;

select * from wallet_history
where wallet_fk=15249
and event_end_date::DATE =CURRENT_DATE

select * from transactions
where id =375902

      SELECT id, wallet_id, interest_yield, currency
        FROM public.wallet_interest_transaction
        WHERE transaction_date = current_date - INTERVAL '1 day'
          AND process_status = 'Pending'



WITH tiered_calc AS (
    SELECT 
        wallet_id,
        interest_yield,
        max_amount,
        annual_rate,
        COALESCE(LAG(max_amount) OVER (PARTITION BY wallet_id ORDER BY tier_order), 0) AS prev_max_amount
    FROM public.wallet_interest_transaction t
    JOIN public.wallet_interest_tiers it ON it.effective_date <= CURRENT_DATE
    WHERE t.wallet_id = 299 AND t.transaction_date = CURRENT_DATE
)
SELECT 
    SUM(
        CASE
            WHEN max_amount IS NULL THEN (interest_yield - prev_max_amount)
            ELSE LEAST(interest_yield - prev_max_amount, max_amount - prev_max_amount)
        END * (annual_rate / 365)
    ) AS interest
--INTO v_interest
FROM tiered_calc
GROUP BY wallet_id;









		  

---  Check 
SELECT * FROM cron.job;

--- to know the execution history --
SELECT * FROM cron.job_run_details ORDER BY start_time DESC LIMIT 10;

-- to check failed execution history ---
SELECT * FROM cron.job_run_details WHERE status = 'failed' ORDER BY start_time DESC;

--- backend checking of the activities 
SELECT * FROM pg_stat_activity WHERE query LIKE '%cron%';

--- Enforce logging of all cron activities
ALTER SYSTEM SET cron.log_statement = 'all';
SELECT pg_reload_conf();

-------check
 SELECT DISTINCT w.id AS wallet_id
        FROM public.wallet w
        LEFT JOIN public.wallet_history t ON w.id = t.wallet_fk
        WHERE DATE(t.event_end_date) = DATE(NOW()) - INTERVAL '2 day'
           OR DATE(t.event_end_date) = DATE(NOW())  - INTERVAL '1 day';




