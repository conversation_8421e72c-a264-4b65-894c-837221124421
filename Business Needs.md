WALLET - Enhancement								
1. Extend Wallet								
	a. Withdrawal Count							
	b. Earned_Interest_todate							
	c. Current_Month_Earn_Interest							
2. Use Wallet Settings 	(For parameter) (Next_Month_Run -include )-- WHT rate, Annual Interest rate, Withdrawal Limit,							
3. Daily								
	a. Use Wallet_Batch_Log - To update the Scheduler daily activities							
	b. Runs interest calculation based on daily net value - (Use Wallet Available_balance) - Update Wallet_interest_transaction (ID,wallet_id, transaction_date,balance, process_type(daily/Monthly), interest_earned, Fee, currency.							
4. Monthly								
	1. Withdrawal Counter - reset to 0							
	2a. If counter less than limit - Update - Transaction table, Wallet_history, Wallet with the earned amount,  fee(wht).							
	2b. if not - skipped.   Return value is zero							
	3. Reset Current_month_Earn_interest to 0							
5. Push to Notification	 - Monthly Earned total value							
								
6. Front End..								
	a. During Withdrawal via Wallet show customer remaining available withdrawal count							
	b. Update Wallet Fetch details to include Current_month_earn_interest, Earned_interest_todate, withdraw_counter, Withdrawl Limit, Wallet Int rate							