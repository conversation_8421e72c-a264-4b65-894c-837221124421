

-- Indexing by time (in descending order) is most common for logs.
CREATE INDEX IF NOT EXISTS idx_wallet_batch_log_logtime_desc ON public.wallet_batch_log(logtime DESC);

-- You could also create a composite index if you often filter by event type.
CREATE INDEX IF NOT EXISTS idx_wallet_batch_log_event_logtime ON public.wallet_batch_log(event, logtime DESC);

-- This creates a unique index, ensuring fast key-based lookups and data integrity.
CREATE UNIQUE INDEX IF NOT EXISTS idx_wallet_interest_settings_key ON public.wallet_interest_settings(key);

-- This composite index directly supports the WHERE clause in the procedure.
-- `status` is first because it has low cardinality (true/false) and will narrow down the initial set.
CREATE INDEX IF NOT EXISTS idx_wallet_status_available_balance ON public.wallet(status, available_balance);

-- This allows you to efficiently look up the interest history for a given wallet, ordered by date.
CREATE INDEX IF NOT EXISTS idx_wallet_interest_transaction_wallet_id_date ON public.wallet_interest_transaction(wallet_id, transaction_date DESC);
-- Index the new column for fast joins back to the wallet.--considering using wallet Id in transaction table... 
--CREATE INDEX IF NOT EXISTS idx_transactions_wallet_id ON public.transactions(wallet_id);

CREATE INDEX IF NOT EXISTS idx_transaction_fee_transaction_fk ON public.transaction_fee(transaction_fk);
CREATE INDEX IF NOT EXISTS idx_wallet_history_transaction_fk ON public.wallet_history(transaction_fk);

-----  After a while -- run performance analysse --
ANALYZE public.wallet;
ANALYZE public.transactions;
ANALYZE public.wallet_interest_transaction;
-- etc., for all tables involved in the procedures.
--Tune Autovacuum: These procedures perform a high volume of UPDATEs and INSERTs, which creates "dead tuples." While AWS RDS manages autovacuum for you, for tables like wallet that see heavy updates, you may need to tune the autovacuum settings to be more aggressive to reclaim space and prevent performance degradation.
--Monitor AWS RDS Performance Insights: Use Performance Insights on your RDS dashboard to identify bottlenecks. It will visually show you which queries are taking the most time and what the wait events are (e.g., I/O waits, CPU waits). This is the most effective way to confirm if your indexes are being used correctly.
--Check IOPS: Batch jobs are I/O intensive. Monitor your Read and Write IOPS (Input/Output Operations Per Second) in CloudWatch. If you are consistently hitting your IOPS ceiling, consider switching to Provisioned IOPS (io1 or gp3) storage for your RDS instance to guarantee performance
--