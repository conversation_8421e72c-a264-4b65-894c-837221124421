-- PROCEDURE: public.wallet_daily_interest_opt(date)

-- DROP PROCEDURE IF EXISTS public.wallet_daily_interest_opt(date);

CREATE OR REPLACE PROCEDURE public.wallet_daily_interest_opt(
	IN p_processing_date date)
LANGUAGE 'plpgsql'
AS $BODY$
DECLARE
    v_okay INT := 0;
    v_failed INT := 0; -- In this set-based approach, this will likely remain 0 unless the whole transaction fails.
    v_batch_msg VARCHAR;
    v_int_rate NUMERIC;
    v_minimum NUMERIC;
    v_year INT;
    v_previous_day DATE := p_processing_date - INTERVAL '1 day';
BEGIN
    -- Fetch settings
    SELECT value INTO v_int_rate FROM public.wallet_interest_settings WHERE UPPER(key) = UPPER('Wallet_Interest');
    SELECT value INTO v_minimum FROM public.wallet_interest_settings WHERE UPPER(key) = UPPER('min_balance_allowed');

	v_int_rate := COALESCE(v_int_rate,0.01);
	v_minimum := COALESCE (v_minimum,0);

    -- Determine the number of days in the year for the interest calculation
    SELECT
        CASE
            WHEN EXTRACT(YEAR FROM v_previous_day) % 4 = 0 AND (EXTRACT(YEAR FROM v_previous_day) % 100 != 0 OR EXTRACT(YEAR FROM v_previous_day) % 400 = 0)
            THEN 366
            ELSE 365
        END
    INTO v_year;

    --Using CTE, set-based approach
    WITH interest_calculation AS (
        -- Calculate daily interest for all eligible wallets
        SELECT
            id,
            available_balance,
            currency,
            ROUND(available_balance * (v_int_rate / v_year), 2) AS daily_interest
        FROM
            public.wallet
        WHERE
            status = TRUE
            AND available_balance >= v_minimum
    ),
    updated_wallets AS (
        -- Atomically update all eligible wallets with their calculated daily interest
        UPDATE
            public.wallet w
        SET
            current_month_earn_interest = COALESCE(w.current_month_earn_interest,0) + i.daily_interest
        FROM
            interest_calculation i
        WHERE
            w.id = i.id
        RETURNING w.id
    )
    -- Insert all successful interest transactions in a single operation
    INSERT INTO wallet_interest_transaction
        (wallet_id, transaction_date, balance, process_status, processed_at, currency, interest_earned, fees, process_type)
    SELECT
        i.id,
        v_previous_day,
        i.available_balance,
        'Successful',
        NOW(),
        i.currency,
        i.daily_interest,
        0.0,
        'Daily'
    FROM
        interest_calculation i
    -- Ensure we only log for wallets that were successfully updated.
    JOIN
        updated_wallets uw ON i.id = uw.id;

    -- Get the count of processed wallets
    GET DIAGNOSTICS v_okay = ROW_COUNT;

    -- Final batch log
    v_batch_msg := 'Processed for ' || v_previous_day || ', Success count:- ' || v_okay || ', Failed count:- ' || v_failed;

    INSERT INTO public.wallet_batch_log(event, message, logtime)
    VALUES ('Wallet_Daily_Interest', v_batch_msg, CURRENT_TIMESTAMP);

EXCEPTION
    WHEN OTHERS THEN
        -- If any part of the set-based operation fails, the transaction will be rolled back.
        -- Log the error for debugging.
        v_batch_msg := 'An error occurred during Wallet_Daily_Interest processing for ' || v_previous_day || '. Error: ' || SQLERRM;
        INSERT INTO public.wallet_batch_log(event, message, logtime)
        VALUES ('Wallet_Daily_Interest_Error', v_batch_msg, CURRENT_TIMESTAMP);
END;
$BODY$;
ALTER PROCEDURE public.wallet_daily_interest_opt(date)
    OWNER TO "TevcDev"; ---- change to production users
