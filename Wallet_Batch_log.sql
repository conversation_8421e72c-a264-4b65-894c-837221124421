-- Table: public.wallet_batch_log

-- DROP TABLE IF EXISTS public.wallet_batch_log;

CREATE TABLE IF NOT EXISTS public.wallet_batch_log
(
    id integer NOT NULL DEFAULT nextval('wallet_batch_log_id_seq'::regclass),
    logtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    event text COLLATE pg_catalog."default" NOT NULL,
    message text COLLATE pg_catalog."default" NOT NULL,
    CONSTRAINT wallet_batch_log_pkey PRIMARY KEY (id)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.wallet_batch_log
    OWNER to digitalspacedb2020;

GRANT ALL ON TABLE public.wallet_batch_log TO "TevcDev";

GRANT ALL ON TABLE public.wallet_batch_log TO digitalspacedb2020;
-- Index: idx_wallet_batch_log_event_logtime

-- DROP INDEX IF EXISTS public.idx_wallet_batch_log_event_logtime;

CREATE INDEX IF NOT EXISTS idx_wallet_batch_log_event_logtime
    ON public.wallet_batch_log USING btree
    (event COLLATE pg_catalog."default" ASC NULLS LAST, logtime DESC NULLS FIRST)
    TABLESPACE pg_default;
-- Index: idx_wallet_batch_log_logtime_desc

-- DROP INDEX IF EXISTS public.idx_wallet_batch_log_logtime_desc;

CREATE INDEX IF NOT EXISTS idx_wallet_batch_log_logtime_desc
    ON public.wallet_batch_log USING btree
    (logtime DESC NULLS FIRST)
    TABLESPACE pg_default;